import React from "react";
import Image from "next/image";
import TROPHY from "@/public/icons/trophy.png";
const InterviewCard = () => {
  return (
    <div className="flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5">
      {/* Left Box: Score Section */}
      <div className="flex items-center space-x-4">
        <div className="bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30">
          <div className="flex justify-center mb-2">
            <Image src={TROPHY} alt="Trophy" />
          </div>
          <p className="text-xl font-bold text-[#1E1E1E]">55%</p>
          <p className="text-xs text-gray-600 mt-1">Overall Score</p>
        </div>

        <div>
          <h3 className="font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2">
            AI Interviewer
          </h3>
          <p className="text-sm text-gray-800 font-medium">UI UX Designer</p>
          <p className="text-sm text-gray-800 font-medium">18th June, 2025</p>
        </div>
      </div>

      <div className="top-0">
        <span className="bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full">
          Evaluated
        </span>
      </div>
    </div>
  );
};

export default InterviewCard;
