import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

const CircularRating = ({ label, percent, color, trailColor }) => {
  return (
    <div className="flex flex-col items-center space-y-1 mb-2">
      <p className="text-sm font-semibold mb-3">{label}</p>
      <div className="w-32 h-28">
        <CircularProgressbar
          value={percent}
          text={`${percent}%`}
          strokeWidth={10}
          styles={buildStyles({
            textSize: "12px",
            pathColor: color,
            textColor: "#5a5a5a",
            trailColor: trailColor,
          })}
        />
      </div>
    </div>
  );
};

export default CircularRating;
