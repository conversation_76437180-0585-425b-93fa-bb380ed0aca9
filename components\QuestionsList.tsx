"use client";
import { useInterview } from "@/context/InterviewContext";

type QuestionsListProps = {
  className?: string;
};

const QuestionsList = ({
  className,
}: QuestionsListProps) => {
  const { questions, currentQuestion } = useInterview();

  return (
    <div
      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${
        className || ""
      }`}
    >
      {" "}
      <h3 className="font-semibold text-lg mb-6">Questions</h3>
      <ul className="relative space-y-8  ">
        {Array.from({ length: 4 }, (_, i) => (
          <li
            key={i}
            className="relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5"
          >
            {i !== 3 && (
              <span className="absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]" />
            )}
            <div
              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${
                i + 1 === currentQuestion
                  ? "bg-[#6938EF] text-white"
                  : i + 1 < currentQuestion
                    ? "bg-green-500 text-white"
                    : "bg-[#C7ACF5] text-white"
              }`}
            >
              {i + 1 < currentQuestion ? "✓" : i + 1}
            </div>
            <span
              className={`text-md font-medium mt-7 ${
                i + 1 === currentQuestion
                  ? "text-[#6938EF] font-semibold"
                  : "text-[#616161]"
              }`}
            >
              {questions[i]}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default QuestionsList;
