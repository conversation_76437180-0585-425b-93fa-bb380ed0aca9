import ScoreBar from "./ScoreBar";
import CircularRating from "./CircularRating";

const ScoreCard = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto">
      {/* Resume Score */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex justify-between font-semibold mb-4">
          <span>Resume Score</span>
          <span>65%</span>
        </div>
        <div className="flex flex-col gap-4">
          <ScoreBar label="Company Fit" value={66} />
          <ScoreBar
            label="Relevant Experience"
            value={66}
            color="bg-purple-600"
          />
          <ScoreBar label="Job Knowledge" value={66} />
          <ScoreBar label="Education" value={66} />
          <ScoreBar label="Hard Skills" value={66} />
        </div>

        <div className="mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8">
          Over All Score &nbsp; <span className="text-black">66/100</span>
        </div>
      </div>

      {/* Video Score */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="font-semibold mb-4">Video Score</div>
        <div className="flex flex-col gap-4">
          <ScoreBar label="Professionalism" value={64} />
          <ScoreBar label="Energy Level" value={56} color="bg-purple-600" />
          <ScoreBar label="Communication" value={58} />
          <ScoreBar label="Sociability" value={70} />
        </div>
      </div>

      {/* AI Ratings */}
      <div className="bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm">
        <p className="font-semibold">AI Rating</p>
        <CircularRating
          label="AI Resume Rating"
          percent={75}
          color="#A855F7"
          trailColor="#EAE2FF"
        />
        <CircularRating
          label="AI Video Rating"
          percent={75}
          color="#FF5B00"
          trailColor="#FFEAE1"
        />
      </div>
    </div>
  );
};

export default ScoreCard;
