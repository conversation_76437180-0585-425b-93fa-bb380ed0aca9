"use client";

import React, { useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import LOGO from "../public/images/logo-light.svg";
import { LayoutDashboard, BriefcaseBusiness, X } from "lucide-react";

const sidebarItems = [
  {
    label: "Dashboard",
    href: "/",
    icon: LayoutDashboard,
  },
  {
    label: "Job Posts",
    href: "/interview",
    icon: BriefcaseBusiness,
  },
];

const Sidebar = ({
  isOpen,
  onClose,
}: {
  isOpen?: boolean;
  onClose?: () => void;
}) => {
  const pathname = usePathname();
  const previousPathname = useRef(pathname);

  // Close mobile sidebar when route changes (but not on initial mount)
  useEffect(() => {
    if (previousPathname.current !== pathname && isOpen && onClose) {
      onClose();
    }
    previousPathname.current = pathname;
  }, [pathname, isOpen, onClose]);

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar");
      const overlay = document.getElementById("sidebar-overlay");
      if (
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        overlay?.contains(event.target as Node)
      ) {
        if (onClose) onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className="hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0">
        {/* Logo */}
        <div className="flex items-center gap-2 mb-10">
          <Image src={LOGO} alt="Logo" />
        </div>

        {/* Navigation */}
        <nav className="flex flex-col gap-4">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link
                key={item.label}
                href={item.href}
                className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                  ${
                    isActive
                      ? "bg-purple-100 text-purple-700 font-extrabold"
                      : "text-gray-400 hover:bg-gray-50 hover:text-gray-600"
                  }`}
              >
                <Icon
                  className={`w-5 h-5 ${
                    isActive ? "text-purple-700" : "text-gray-400"
                  }`}
                />
                <span className="text-sm font-medium">{item.label}</span>
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Mobile/Tablet Sidebar Overlay */}
      <div
        id="sidebar-overlay"
        className={`fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ${
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
      >
        <aside
          id="mobile-sidebar"
          className={`fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out ${
            isOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          {/* Mobile Sidebar Header */}
          <div className="flex items-center justify-between mb-10">
            <Image src={LOGO} alt="Logo" />
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          {/* Mobile Navigation */}
          <nav className="flex flex-col gap-4">
            {sidebarItems.map((item) => {
              const isActive = pathname === item.href;
              const Icon = item.icon;

              return (
                <Link
                  key={item.label}
                  href={item.href}
                  className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all group
                      ${
                        isActive
                          ? "bg-purple-100 text-purple-700 font-extrabold"
                          : "text-gray-400 hover:bg-gray-50 hover:text-gray-600"
                      }`}
                  onClick={onClose}
                >
                  <Icon
                    className={`w-5 h-5 ${
                      isActive ? "text-purple-700" : "text-gray-400"
                    }`}
                  />
                  <span className="text-sm font-medium">{item.label}</span>
                </Link>
              );
            })}
          </nav>
        </aside>
      </div>
    </>
  );
};

export default Sidebar;
